# Research: Business Analytics Dashboard

## Executive Summary

- **Feature Description**: Comprehensive analytics dashboard for business owners to track listing performance, including views, clicks, conversion metrics, and geographic insights
- **Scope and Impact**: Tests integration between analytics system, real-time data updates, role-based access control, and data visualization components
- **Estimated Complexity**: High - involves multiple system layers (frontend, backend, database, real-time updates, role management)

## Feature Analysis

### User Interactions

Based on research of analytics dashboard implementations, the business analytics dashboard will provide:

1. **Performance Metrics Overview**
   - Key performance indicators (KPIs) cards showing total views, clicks, conversions
   - Percentage change indicators with trend arrows
   - Real-time data updates with connection status indicators

2. **Time-based Trend Analysis**
   - Interactive charts showing performance over time (7d, 30d, 90d)
   - Line charts for views/clicks trends using Recharts
   - Bar charts for conversion metrics
   - Date range picker for custom time periods

3. **Geographic Distribution**
   - Map visualization showing engagement by location
   - Heatmap overlay for view density
   - Geographic breakdown tables

4. **Conversion Funnel Visualization**
   - Step-by-step conversion tracking
   - Funnel charts showing drop-off rates
   - Attribution analysis for traffic sources

5. **Export and Reporting**
   - CSV/PDF export functionality
   - Scheduled report generation
   - Email report delivery

### Edge Cases & Constraints

**Error Scenarios:**
- Network connectivity issues affecting real-time updates
- Large datasets causing performance degradation
- Missing or corrupted analytics data
- Role permission conflicts

**Boundary Conditions:**
- Zero data states for new business listings
- Maximum data limits for chart rendering
- Time zone handling for global businesses
- Mobile responsiveness constraints

**Security Considerations:**
- Role-based data access (owners see only their data)
- Data privacy compliance
- Secure API endpoints for analytics data
- Input validation for date ranges and filters

**Performance Limitations:**
- Chart rendering with large datasets (>10k points)
- Real-time update frequency limits
- Memory usage with multiple concurrent dashboards
- Database query optimization for analytics aggregations

## Technical Research

### Web Search Findings

**Analytics Dashboard Patterns:**
- **Latitude Analytics** - Developer-first embedded analytics with TypeScript
- **Briefer Dashboard** - Notebooks and dashboards with real-time capabilities
- **Tremor Dashboard Template** - Open-source dashboard using Recharts and Next.js
- **Payload Dashboard Analytics** - Plugin-based analytics with Google Analytics integration

**Key Technologies Identified:**
- **Recharts** - Most popular React charting library (used in Tremor template)
- **Real-time Updates** - WebSocket/SSE patterns for live data
- **Role-based Access** - Middleware-based permission checking
- **Data Visualization** - Card-based metric displays with trend indicators

### Codebase Analysis

**Existing Components to Leverage:**
- `convex/analytics.ts:55-112` - getListingAnalytics query with role-based access
- `hooks/useAnalytics.ts:70-240` - Analytics tracking with session management
- `hooks/useAdminAnalytics.ts:41-217` - Real-time analytics with update notifications
- `components/ui/card.tsx` - Base card components for metric displays
- `app/dashboard/admin/analytics/page.tsx:92-120` - Existing admin analytics structure

**Similar Patterns Found:**
- `app/dashboard/owner/page.tsx:127-139` - Summary cards pattern for metrics
- `components/custom/AdminNotifications.tsx` - Real-time update notifications
- `hooks/useModerationStatus.ts` - Real-time data with status tracking
- `convex/schema.ts:150-170` - Analytics events schema structure

**Required Modifications:**
- Extend `convex/analytics.ts` with owner-specific queries
- Create new hook `useOwnerAnalytics.ts` for business owner data
- Build chart components using existing design system
- Add geographic analytics queries and components

**State Management Impacts:**
- Real-time Convex queries for live dashboard updates
- Local state for chart interactions and filters
- Session storage for dashboard preferences
- Error boundary handling for chart failures

## Reference Index

**External Documentation:**
- Recharts Documentation - https://recharts.org/en-US/
- Tremor Dashboard Template - https://github.com/tremorlabs/template-dashboard-oss
- Payload Analytics Plugin - https://github.com/NouanceLabs/payload-dashboard-analytics
- Latitude Analytics - https://github.com/latitude-dev/latitude

**Relevant Code Locations:**
- convex/analytics.ts:55-112 - Existing analytics queries
- hooks/useAnalytics.ts:70-240 - Analytics tracking system
- hooks/useAdminAnalytics.ts:41-217 - Real-time analytics patterns
- app/dashboard/owner/page.tsx:127-139 - Owner dashboard structure
- components/ui/card.tsx - Base UI components
- convex/schema.ts:150-170 - Analytics data schema

**Library/Framework References:**
- Recharts v2.15.1 - React charting library
- Next.js 15 - App Router patterns
- Convex - Real-time database queries
- Tailwind CSS v4 - Design system styling
- Radix UI - Accessible component primitives

**Related Issues or Discussions:**
- Role-based analytics access patterns
- Real-time dashboard performance optimization
- Chart rendering with large datasets
- Geographic data visualization techniques

## Implementation Strategy

### Phase 1: Backend Analytics Extensions
1. **Extend Convex Analytics Functions**
   - Create `getOwnerListingAnalytics` query for business-specific metrics
   - Add geographic aggregation queries
   - Implement time-series data queries for charts
   - Add conversion funnel tracking queries

2. **Schema Enhancements**
   - Add geographic metadata to analytics events
   - Create analytics aggregation tables for performance
   - Add conversion tracking fields

### Phase 2: Frontend Dashboard Components
1. **Analytics Hook Development**
   - Create `useOwnerAnalytics` hook for business owner data
   - Implement real-time updates with Convex subscriptions
   - Add error handling and loading states

2. **Chart Components**
   - Build Recharts-based metric cards
   - Create time-series line/bar charts
   - Implement geographic visualization components
   - Add conversion funnel charts

### Phase 3: Dashboard Integration
1. **Owner Dashboard Page**
   - Create `/dashboard/owner/analytics` route
   - Integrate with existing owner navigation
   - Add role-based access protection

2. **Real-time Features**
   - Live data updates with connection indicators
   - Notification system for significant changes
   - Export functionality for reports

### Phase 4: Performance & Polish
1. **Optimization**
   - Chart rendering performance with large datasets
   - Database query optimization
   - Caching strategies for aggregated data

2. **User Experience**
   - Mobile responsiveness
   - Loading states and error boundaries
   - Accessibility compliance

## Technical Specifications

### Required Dependencies
- Recharts (already installed) - Chart rendering
- Date-fns (for date manipulation)
- React-window (for large dataset virtualization)

### New Files to Create
- `hooks/useOwnerAnalytics.ts` - Owner-specific analytics hook
- `components/custom/AnalyticsDashboard.tsx` - Main dashboard component
- `components/custom/MetricCard.tsx` - KPI display component
- `components/custom/AnalyticsChart.tsx` - Chart wrapper component
- `app/dashboard/owner/analytics/page.tsx` - Analytics page route
- `convex/ownerAnalytics.ts` - Owner-specific analytics queries

### Integration Points
- Extends existing analytics system without breaking admin functionality
- Uses established role-based access patterns
- Integrates with current design system and navigation
- Leverages existing real-time update infrastructure
